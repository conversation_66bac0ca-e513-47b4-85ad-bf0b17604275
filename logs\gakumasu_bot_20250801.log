2025-08-01 09:43:28 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-08-01 09:43:28 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [33948] using StatReload
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [9300]
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52936 - "GET /health HTTP/1.1" 200 OK
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-08-01 09:43:32 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-08-01 09:43:34 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-08-01 09:43:34 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52988 - "WebSocket /ws" [accepted]
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52990 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52992 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52999 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53000 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53002 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53001 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53012 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53011 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53026 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53025 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53023 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53024 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53032 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53034 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53081 - "WebSocket /ws" [accepted]
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53083 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-08-01 09:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53085 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53097 - "WebSocket /ws" [accepted]
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53099 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-08-01 09:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53101 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败:
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55508 - "WebSocket /ws" [accepted]
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55510 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55512 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:57:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:57:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55722 - "WebSocket /ws" [accepted]
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55726 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55725 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55742 - "WebSocket /ws" [accepted]
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55744 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55746 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 09:58:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:58:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-08-01 09:59:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55902 - "GET /api/v1/screenshot/preview HTTP/1.1" 200 OK
2025-08-01 09:59:18 - GakumasuBot - INFO - gui.py:146 - Backend: WebSocket连接错误: WebSocket is not connected. Need to call "accept" first.
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55924 - "WebSocket /ws" [accepted]
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55926 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 09:59:22 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55928 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:00:06 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-08-01 10:00:06 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-08-01 10:00:06 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-08-01 10:00:06 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-08-01 10:06:16 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-08-01 10:06:16 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [18488] using StatReload
2025-08-01 10:06:19 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [21836]
2025-08-01 10:06:19 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-08-01 10:06:19 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-08-01 10:06:20 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:57126 - "GET /health HTTP/1.1" 200 OK
2025-08-01 10:06:20 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-08-01 10:06:20 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-08-01 10:06:20 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-08-01 10:06:22 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-08-01 10:06:22 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:57155 - "WebSocket /ws" [accepted]
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:57157 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:06:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:57159 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:07:36 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-08-01 10:07:36 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-08-01 10:07:36 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-08-01 10:07:36 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-08-01 10:34:35 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-08-01 10:34:35 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-08-01 10:34:35 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-08-01 10:34:35 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-08-01 10:34:35 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-08-01 10:34:35 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-08-01 10:34:35 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-08-01 10:34:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-08-01 10:34:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-08-01 10:34:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [43000] using StatReload
2025-08-01 10:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [19416]
2025-08-01 10:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-08-01 10:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-08-01 10:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65352 - "GET /health HTTP/1.1" 200 OK
2025-08-01 10:34:39 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-08-01 10:34:39 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-08-01 10:34:39 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-08-01 10:34:41 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-08-01 10:34:41 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-08-01 10:34:43 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65383 - "WebSocket /ws" [accepted]
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65385 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65387 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49402 - "WebSocket /ws" [accepted]
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49404 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49406 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49561 - "WebSocket /ws" [accepted]
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49563 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49565 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49572 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49573 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49574 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49575 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:37:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49585 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:37:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49586 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:37:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49598 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:37:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49599 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:37:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49600 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:37:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49601 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:37:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49606 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:37:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49608 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50049 - "WebSocket /ws" [accepted]
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50052 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50053 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50084 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50086 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50322 - "WebSocket /ws" [accepted]
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50324 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50326 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50475 - "WebSocket /ws" [accepted]
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50477 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50479 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51287 - "WebSocket /ws" [accepted]
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51289 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51291 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51302 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51300 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51301 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51303 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51311 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51313 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:46:00 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51322 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:46:00 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51325 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:46:00 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51326 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:46:00 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51327 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:46:00 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51338 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:46:00 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:51340 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 200 OK
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52489 - "WebSocket /ws" [accepted]
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52492 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:52:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:52493 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53038 - "WebSocket /ws" [accepted]
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:55:55 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53041 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:55:56 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53049 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53241 - "WebSocket /ws" [accepted]
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53244 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53245 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53252 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53254 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53253 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53255 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53271 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53278 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53273 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53275 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53277 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 10:56:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53279 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 10:56:50 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53288 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 10:56:50 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53292 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 304 Not Modified
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53447 - "WebSocket /ws" [accepted]
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53449 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 10:57:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:53451 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 11:04:34 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-08-01 11:04:34 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-08-01 11:04:34 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-08-01 11:04:34 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-08-01 11:22:42 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-08-01 11:22:42 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-08-01 11:22:42 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-08-01 11:22:42 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-08-01 11:22:42 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-08-01 11:22:42 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-08-01 11:22:42 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-08-01 11:22:42 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-08-01 11:22:42 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-08-01 11:22:42 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [21820] using StatReload
2025-08-01 11:22:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [14408]
2025-08-01 11:22:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-08-01 11:22:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-08-01 11:22:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:58037 - "GET /health HTTP/1.1" 200 OK
2025-08-01 11:22:46 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-08-01 11:22:46 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-08-01 11:22:46 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-08-01 11:22:48 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-08-01 11:22:48 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-08-01 11:22:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:58066 - "WebSocket /ws" [accepted]
2025-08-01 11:22:55 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:58107 - "WebSocket /ws" [accepted]
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:58109 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 11:22:56 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:58111 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60337 - "WebSocket /ws" [accepted]
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60339 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 11:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60341 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60407 - "WebSocket /ws" [accepted]
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60410 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 11:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60411 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: 截图收集器错误: 区域截图需要指定区域坐标
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [CRITICAL] 截图API端点被调用！
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 原始请求: mode=ScreenshotModeEnum.REGION (<enum 'ScreenshotModeEnum'>), format=ScreenshotFormatEnum.PNG (<enum 'ScreenshotFormatEnum'>)
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换后的配置: mode=region (<class 'str'>), format=png (<class 'str'>)
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 完整配置字典: {'mode': 'region', 'format': 'png', 'quality': 90, 'region': None, 'save_to_disk': True, 'filename_prefix': 'screenshot'}
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [INFO] 开始执行截图，配置: {'mode': 'region', 'format': 'png', 'quality': 90, 'region': None, 'save_to_disk': True, 'filename_prefix': 'screenshot'}
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] _convert_screenshot_config 接收到配置: {'mode': 'region', 'format': 'png', 'quality': 90, 'region': None, 'save_to_disk': True, 'filename_prefix': 'screenshot'}
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 原始模式值: region (类型: <class 'str'>)
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 模式值来自字符串: region
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 准备转换模式字符串 'region' 为 ScreenshotMode 枚举
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 模式转换完成: region -> ScreenshotMode.REGION (类型: <enum 'ScreenshotMode'>)
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 原始格式值: png (类型: <class 'str'>)
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 格式值来自字符串: png
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 准备转换格式字符串 'png' 为 ScreenshotFormat 枚举
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 格式转换完成: png -> ScreenshotFormat.PNG (类型: <enum 'ScreenshotFormat'>)
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 最终转换的配置对象: mode=ScreenshotMode.REGION (类型: <enum 'ScreenshotMode'>), format=ScreenshotFormat.PNG
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: [INFO] 截图执行完成，成功: False, 错误: 区域截图需要指定区域坐标
2025-08-01 11:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60531 - "POST /api/v1/screenshot/capture HTTP/1.1" 200 OK
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64970 - "WebSocket /ws" [accepted]
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64972 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 12:06:54 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64974 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64991 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 12:07:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64992 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65038 - "WebSocket /ws" [accepted]
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65040 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 12:07:18 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:65042 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:57656 - "WebSocket /ws" [accepted]
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:57659 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 13:13:41 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:57660 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 13:26:27 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-08-01 13:26:27 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-08-01 13:26:27 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-08-01 13:26:27 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-08-01 13:26:29 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-08-01 13:26:29 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-08-01 13:26:29 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-08-01 13:26:29 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-08-01 13:26:29 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-08-01 13:26:29 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-08-01 13:26:29 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-08-01 13:26:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-08-01 13:26:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-08-01 13:26:29 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [38164] using StatReload
2025-08-01 13:26:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [38860]
2025-08-01 13:26:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-08-01 13:26:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-08-01 13:26:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60186 - "WebSocket /ws" [accepted]
2025-08-01 13:26:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60188 - "WebSocket /ws" [accepted]
2025-08-01 13:26:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60193 - "GET /health HTTP/1.1" 200 OK
2025-08-01 13:26:33 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-08-01 13:26:33 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-08-01 13:26:33 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-08-01 13:26:35 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-08-01 13:26:35 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60243 - "WebSocket /ws" [accepted]
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60246 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 13:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60248 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:62954 - "WebSocket /ws" [accepted]
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:62957 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 13:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:62958 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64456 - "WebSocket /ws" [accepted]
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64458 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 13:48:13 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64460 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 13:48:58 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-08-01 13:48:58 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-08-01 13:48:58 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-08-01 13:48:58 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-08-01 13:48:59 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-08-01 13:48:59 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-08-01 13:48:59 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-08-01 13:48:59 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-08-01 13:48:59 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-08-01 13:48:59 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-08-01 13:48:59 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-08-01 13:48:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-08-01 13:48:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-08-01 13:48:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [42016] using StatReload
2025-08-01 13:49:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [16316]
2025-08-01 13:49:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-08-01 13:49:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-08-01 13:49:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64629 - "WebSocket /ws" [accepted]
2025-08-01 13:49:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64640 - "GET /health HTTP/1.1" 200 OK
2025-08-01 13:49:03 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-08-01 13:49:03 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-08-01 13:49:03 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-08-01 13:49:05 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-08-01 13:49:05 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64688 - "WebSocket /ws" [accepted]
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64691 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-08-01 13:49:08 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64692 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-08-01 13:50:32 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-08-01 13:50:32 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-08-01 13:50:32 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-08-01 13:50:32 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
